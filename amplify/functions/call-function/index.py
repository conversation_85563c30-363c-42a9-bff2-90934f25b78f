# functions/call-function/index.py

import json
import os
import sys
import uuid
import boto3
import tempfile

# NLTK setup - 确保在Lambda环境中正确设置
os.environ['NLTK_DATA'] = '/opt/nltk_data'

# 简化的句子分割函数，不依赖NLTK数据
def sent_tokenize(text):
    """简化的句子分割，不依赖NLTK punkt数据"""
    import re
    if not text:
        return []
    # 使用正则表达式分割句子
    sentences = re.split(r'[.!?]+\s+', text)
    # 清理并过滤空句子
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 3:  # 过滤太短的句子
            cleaned_sentences.append(sentence)
    return cleaned_sentences

# 尝试导入NLTK，但不依赖它
try:
    import nltk
    print("CITEAI DEBUG: NLTK imported successfully", file=sys.stderr)
    # 不尝试加载punkt数据，直接使用我们的简化版本
except ImportError:
    print("CITEAI DEBUG: NLTK not available, using simple tokenizer", file=sys.stderr)

# 导入helper模块 - 确保路径正确
try:
    print("CITEAI DEBUG: Attempting to import helper modules...", file=sys.stderr)

    # 添加当前目录到Python路径
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    print(f"CITEAI DEBUG: Current directory: {current_dir}", file=sys.stderr)
    print(f"CITEAI DEBUG: Python path: {sys.path[:3]}", file=sys.stderr)

    # 逐个导入模块以便精确定位问题
    try:
        from helpers.utils import (
            TRIGGER_KEYWORDS,
            build_csl_json,
            deduplicate_csl_items,
            num_citations,
            compute_paper_target,
            word_count_fast,
            parse_multiple_sources
        )
        print("CITEAI DEBUG: Successfully imported utils module", file=sys.stderr)
    except ImportError as utils_error:
        print(f"CITEAI CRITICAL ERROR: Utils module import failed: {utils_error}", file=sys.stderr)
        raise ImportError(f"Utils module import failed: {utils_error}. Please contact IT support.")

    try:
        from helpers.scrap import fetch_paper_metadata, extract_article_metadata
        print("CITEAI DEBUG: Successfully imported scrap module", file=sys.stderr)
    except ImportError as scrap_error:
        print(f"CITEAI CRITICAL ERROR: Scrap module import failed: {scrap_error}", file=sys.stderr)
        raise ImportError(f"Scrap module import failed: {scrap_error}. Please contact IT support.")

    try:
        from helpers.perplexity import call_perplexity_api
        print("CITEAI DEBUG: Successfully imported perplexity module", file=sys.stderr)
    except ImportError as perplexity_error:
        print(f"CITEAI CRITICAL ERROR: Perplexity module import failed: {perplexity_error}", file=sys.stderr)
        raise ImportError(f"Perplexity module import failed: {perplexity_error}. Please contact IT support.")

    # 检查citeproc-py可用性（延迟导入filegen以避免WeasyPrint问题）
    try:
        import citeproc
        CITEPROC_AVAILABLE = True
        print("CITEAI DEBUG: citeproc-py is available", file=sys.stderr)
    except ImportError as citeproc_error:
        CITEPROC_AVAILABLE = False
        print(f"CITEAI DEBUG: citeproc-py not available: {citeproc_error}", file=sys.stderr)
        print("CITEAI DEBUG: Will use simplified citation formatter", file=sys.stderr)

    # 导入简化的引用格式化器
    try:
        from helpers.simple_citation import format_citations_simple, generate_html_simple
        print("CITEAI DEBUG: Successfully imported simple citation formatter", file=sys.stderr)
    except ImportError as simple_error:
        print(f"CITEAI CRITICAL ERROR: Simple citation formatter import failed: {simple_error}", file=sys.stderr)
        raise ImportError(f"Simple citation formatter import failed: {simple_error}. Please contact IT support.")

    print("CITEAI DEBUG: All helper modules imported successfully!", file=sys.stderr)

except ImportError as e:
    print(f"CITEAI CRITICAL ERROR: Helper module import failed: {e}", file=sys.stderr)
    import traceback
    traceback.print_exc(file=sys.stderr)
    # 不使用fallback，直接抛出错误
    raise ImportError(f"Critical dependency import failed: {e}. Please contact IT support.")


def process_citation_request(request_data: dict) -> dict:
    """
    核心逻辑处理函数
    """
    try:
        print(f"Processing request data: {json.dumps(request_data, indent=2)}", file=sys.stderr)
        
        # 获取输入参数
        title = request_data.get('title', '')
        content = request_data.get('content', '')
        citation_format = request_data.get('citation_format', 'mla').lower()
        plan = request_data.get('plan', 'free').lower()
        output_mode = request_data.get('output_mode', 'text')
        format_only = request_data.get('format_only', False)
        csl_items = request_data.get('csl_items', [])

        print(f"Parsed parameters: title='{title}', format='{citation_format}', plan='{plan}', mode='{output_mode}'", file=sys.stderr)

        # 验证必需参数
        if not title or not content:
            return {"error": "Missing required fields: title and content"}

        # format_only模式处理
        if format_only and csl_items:
            return handle_format_only(csl_items, citation_format, output_mode)

        # 主要引用生成逻辑
        return generate_citations(title, content, citation_format, plan, output_mode)

    except Exception as e:
        print(f"Error in process_citation_request: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return {"error": str(e)}


def handle_format_only(csl_items, citation_format, output_mode):
    """处理仅格式化模式"""
    try:
        csl_items = deduplicate_csl_items(csl_items)

        if output_mode == 'text':
            if CITEPROC_AVAILABLE:
                try:
                    from helpers.filegen import format_citations_text
                    formatted = format_citations_text(csl_items, citation_format)
                except ImportError as e:
                    print(f"CITEAI DEBUG: Filegen import failed, using simple formatter: {e}", file=sys.stderr)
                    formatted_list = format_citations_simple(csl_items, citation_format)
                    formatted = "\n".join(formatted_list)
            else:
                formatted_list = format_citations_simple(csl_items, citation_format)
                formatted = "\n".join(formatted_list)
            return {"output_type": "text", "data": formatted, "citations": csl_items}

        elif output_mode == 'html':
            if CITEPROC_AVAILABLE:
                try:
                    from helpers.filegen import generate_html
                    html = generate_html(csl_items, citation_format, csl_items)
                except ImportError as e:
                    print(f"CITEAI DEBUG: Filegen import failed, using simple formatter: {e}", file=sys.stderr)
                    formatted_list = format_citations_simple(csl_items, citation_format)
                    html = generate_html_simple(formatted_list, citation_format)
            else:
                formatted_list = format_citations_simple(csl_items, citation_format)
                html = generate_html_simple(formatted_list, citation_format)
            return {"output_type": "html", "data": html, "citations": csl_items}

        else:  # PDF
            if CITEPROC_AVAILABLE:
                return generate_pdf_response(csl_items, citation_format)
            else:
                return {"error": "PDF generation not available without citeproc-py. Please contact IT support."}

    except Exception as e:
        print(f"Error in handle_format_only: {e}", file=sys.stderr)
        return {"error": f"Format only processing failed: {str(e)}"}


def generate_citations(title, content, citation_format, plan, output_mode):
    """主要引用生成逻辑"""
    try:
        print(f"CITEAI DEBUG: Starting citation generation for title='{title}', plan='{plan}', mode='{output_mode}'", file=sys.stderr)
        print(f"CITEAI DEBUG: Content length: {len(content)}", file=sys.stderr)
        
        # 句子分词
        sent_list = sent_tokenize(content)
        if sent_list is None:
            sent_list = []
        print(f"Split content into {len(sent_list)} sentences", file=sys.stderr)

        # 查找标记句子
        flagged_idxs = []
        for i, sent in enumerate(sent_list):
            txt = sent.strip()
            if not txt:
                continue
            if any(phrase in txt.lower() for phrase in TRIGGER_KEYWORDS):
                flagged_idxs.append(i)

        print(f"Found {len(flagged_idxs)} flagged sentences", file=sys.stderr)

        # 计算引用数量
        total_word_count = word_count_fast(content)
        paper_target = compute_paper_target(plan, total_word_count=total_word_count, sample_text=content)
        
        if plan == 'free':
            total_citations = 1
        else:
            total_citations = num_citations(plan, content, paper_target=paper_target, total_word_count=total_word_count)

        print(f"Target citations: {total_citations}", file=sys.stderr)

        generated_csl_items = []

        # 根据计划类型处理
        if plan == 'pro' and flagged_idxs:
            generated_csl_items = process_pro_plan(flagged_idxs, sent_list, title, paper_target, total_word_count)
        else:
            generated_csl_items = process_standard_plan(content, title, plan, total_citations)

        # 后处理
        final_csl_items = deduplicate_csl_items(generated_csl_items)
        
        if plan == 'plus':
            final_csl_items = final_csl_items[:20]
        elif plan == 'free':
            final_csl_items = final_csl_items[:1]

        print(f"Final citation count: {len(final_csl_items)}", file=sys.stderr)
        print(f"Final CSL items preview: {json.dumps(final_csl_items[:2], indent=2) if final_csl_items else 'Empty list'}", file=sys.stderr)

        # 生成输出
        return generate_output(final_csl_items, citation_format, output_mode)

    except Exception as e:
        print(f"Error in generate_citations: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return {"error": f"Citation generation failed: {str(e)}"}





def process_standard_plan(content, title, plan, total_citations):
    """处理标准计划（free/plus）"""
    try:
        print(f"CITEAI DEBUG: Starting process_standard_plan", file=sys.stderr)
        words = content.strip().split()
        context_text = " ".join(words[:150] if plan == 'free' else words[:500])
        
        prompt = (
            f'a paper is being written about "{title}". find up to {total_citations} '
            f'original sources (no duplicates) which would have been used to generate '
            f'the excerpt below. provide me with a url & title of the source and specify '
            f'if it is a research paper or not. the fields you provide should be for a '
            f'source that relatively matches the topic provided. give it in this format '
            f'and do not provide any extra information besides this\n'
            f'URL: [url] \n TITLE: [title]\n Research paper: [yes/no]\n'
            f'excerpt: {context_text}\n'
        )
        
        print(f"CITEAI DEBUG: Calling Perplexity API for standard plan", file=sys.stderr)
        try:
            output = call_perplexity_api(prompt)
            print(f"Raw Perplexity API output: {output}", file=sys.stderr)
        except Exception as api_error:
            print(f"Error calling Perplexity API: {api_error}", file=sys.stderr)
            output = []

        try:
            sources = parse_multiple_sources(output)
            print(f"Perplexity API returned {len(sources)} sources", file=sys.stderr)
            print(f"Parsed sources: {sources}", file=sys.stderr)
        except Exception as parse_error:
            print(f"Error parsing sources: {parse_error}", file=sys.stderr)
            sources = []
        
        generated_items = []
        
        # If no sources from Perplexity, this is a critical error
        if not sources or len(sources) == 0:
            error_msg = "Perplexity API failed to return sources and no fallback available"
            print(f"CITEAI CRITICAL ERROR: {error_msg}", file=sys.stderr)
            raise RuntimeError(f"Citation service unavailable: {error_msg}. Please refresh the page or contact IT support.")
        
        for i, src in enumerate(sources):
            print(f"CITEAI DEBUG: Processing source {i+1}/{len(sources)}", file=sys.stderr)
            try:
                url = src.get('url', '')
                title_src = src.get('title', '')
                is_paper = src.get('is_paper', False)
                
                print(f"Processing source: url='{url}', title='{title_src}', is_paper={is_paper}", file=sys.stderr)
                
                if not url or not title_src:
                    print(f"Skipping source due to missing url or title", file=sys.stderr)
                    continue
                    
                if is_paper:
                    print(f"CITEAI DEBUG: Fetching paper metadata", file=sys.stderr)
                    meta = fetch_paper_metadata(title_src, url)
                    csl = build_csl_json(meta, idx=0) if meta else None
                else:
                    print(f"CITEAI DEBUG: Extracting article metadata", file=sys.stderr)
                    _, csl = extract_article_metadata(url, idx=0)
                    
                if csl and csl.get("title"):
                    print(f"Successfully generated CSL for: {csl.get('title')}", file=sys.stderr)
                    generated_items.append(csl)
                else:
                    print(f"Failed to generate CSL for source: {title_src}", file=sys.stderr)
            except Exception as source_error:
                print(f"Error processing source {i+1}: {source_error}", file=sys.stderr)
                import traceback
                traceback.print_exc(file=sys.stderr)
                continue
                
        print(f"Generated {len(generated_items)} final items", file=sys.stderr)
        return generated_items
        
    except Exception as e:
        print(f"Error in process_standard_plan: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return []


def process_pro_plan(flagged_idxs, sent_list, title, paper_target, total_word_count):
    """处理Pro计划逻辑"""
    try:
        generated_items = []
        already_cited = 0
        
        for idx, i in enumerate(flagged_idxs):
            ctx = []
            for j in (i-1, i, i+1):
                if 0 <= j < len(sent_list):
                    ctx.append(sent_list[j].strip())
            
            context_text = " ".join(" ".join(ctx).split()[:500])
            
            chunk_citations = num_citations(
                'pro', context_text, 
                paper_target=paper_target, 
                total_word_count=total_word_count,
                already_cited=already_cited
            )
            
            if chunk_citations == 0:
                continue
                
            # 调用Perplexity API获取源
            prompt = (
                f'a paper is being written about "{title}". find up to {chunk_citations} '
                f'original sources... excerpt: {context_text}\n'
            )
            
            output = call_perplexity_api(prompt)
            sources = parse_multiple_sources(output)
            
            print(f"Pro plan: Perplexity API returned {len(sources)} sources for chunk {idx}", file=sys.stderr)
            
            # If no sources from Perplexity, this is a critical error
            if not sources or len(sources) == 0:
                error_msg = f"Pro plan: Perplexity API failed for chunk {idx}"
                print(f"CITEAI CRITICAL ERROR: {error_msg}", file=sys.stderr)
                raise RuntimeError(f"Citation service unavailable: {error_msg}. Please refresh the page or contact IT support.")
            
            for src in sources:
                title_src = src.get('title', '')
                url_src = src.get('url', '')
                is_paper = src.get('is_paper', False)
                
                print(f"Pro plan: Processing source: url='{url_src}', title='{title_src}', is_paper={is_paper}", file=sys.stderr)
                
                if not url_src or not title_src:
                    print(f"Pro plan: Skipping source due to missing url or title", file=sys.stderr)
                    continue
                
                if is_paper:
                    meta = fetch_paper_metadata(title_src, url_src)
                    csl = build_csl_json(meta, idx=i) if meta else None
                else:
                    _, csl = extract_article_metadata(url_src, idx=i)
                
                if csl and csl.get("title"):
                    print(f"Pro plan: Successfully generated CSL for: {csl.get('title')}", file=sys.stderr)
                    generated_items.append(csl)
                    already_cited += 1
                else:
                    print(f"Pro plan: Failed to generate CSL for source: {title_src}", file=sys.stderr)
                    
        return generated_items
        
    except Exception as e:
        print(f"Error in process_pro_plan: {e}", file=sys.stderr)
        return []


def generate_output(csl_items, citation_format, output_mode):
    """生成最终输出"""
    try:
        if output_mode == 'text':
            if CITEPROC_AVAILABLE:
                try:
                    from helpers.filegen import format_citations_text
                    formatted = format_citations_text(csl_items, citation_format)
                except ImportError as e:
                    print(f"CITEAI DEBUG: Filegen import failed, using simple formatter: {e}", file=sys.stderr)
                    formatted_list = format_citations_simple(csl_items, citation_format)
                    formatted = "\n".join(formatted_list)
            else:
                # 使用简化格式化器
                formatted_list = format_citations_simple(csl_items, citation_format)
                formatted = "\n".join(formatted_list)

            return {
                "output_type": "text",
                "data": formatted,
                "citations": csl_items,
                "citation_count": len(csl_items)
            }

        elif output_mode == 'html':
            if CITEPROC_AVAILABLE:
                try:
                    from helpers.filegen import generate_html
                    html = generate_html(csl_items, citation_format, csl_items)
                except ImportError as e:
                    print(f"CITEAI DEBUG: Filegen import failed, using simple formatter: {e}", file=sys.stderr)
                    formatted_list = format_citations_simple(csl_items, citation_format)
                    html = generate_html_simple(formatted_list, citation_format)
            else:
                # 使用简化格式化器
                formatted_list = format_citations_simple(csl_items, citation_format)
                html = generate_html_simple(formatted_list, citation_format)

            return {
                "output_type": "html",
                "data": html,
                "citations": csl_items,
                "citation_count": len(csl_items)
            }

        else:  # PDF
            if CITEPROC_AVAILABLE:
                return generate_pdf_response(csl_items, citation_format)
            else:
                return {"error": "PDF generation not available without citeproc-py. Please contact IT support."}

    except Exception as e:
        print(f"Error in generate_output: {e}", file=sys.stderr)
        return {"error": f"Output generation failed: {str(e)}"}


def generate_pdf_response(csl_items, citation_format):
    """生成PDF响应"""
    try:
        try:
            from helpers.filegen import generate_citations_pdf
        except ImportError as import_error:
            error_msg = f"PDF generation not available due to missing dependencies: {import_error}"
            raise RuntimeError(f"PDF service unavailable: {error_msg}. Please contact IT support.")
        
        # 生成本地PDF
        local_pdf_path = generate_citations_pdf(csl_items, citation_format)
        
        # 上传到S3
        s3_bucket = os.environ.get('S3_BUCKET_NAME')
        if not s3_bucket:
            raise ValueError("S3_BUCKET_NAME environment variable not set")

        s3_key = f"citations/{uuid.uuid4()}.pdf"
        s3_client = boto3.client('s3')
        
        s3_client.upload_file(local_pdf_path, s3_bucket, s3_key)
        
        presigned_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': s3_bucket, 'Key': s3_key},
            ExpiresIn=3600
        )
        
        # 清理本地文件
        try:
            os.remove(local_pdf_path)
        except:
            pass
            
        return {
            "output_type": "pdf", 
            "data": {"download_url": presigned_url},
            "citations": csl_items,
            "citation_count": len(csl_items)
        }
        
    except Exception as e:
        print(f"Error in generate_pdf_response: {e}", file=sys.stderr)
        return {"error": f"PDF generation failed: {str(e)}"}





def handler(event, context):
    """
    AWS Lambda处理函数
    """
    try:
        print(f"Lambda invoked with event: {json.dumps(event, indent=2)}", file=sys.stderr)
        
        # 解析请求体
        if 'body' in event:
            if isinstance(event['body'], str):
                request_data = json.loads(event['body'])
            else:
                request_data = event['body']
        else:
            request_data = event

        print(f"Request data parsed: {json.dumps(request_data, indent=2)}", file=sys.stderr)

        # 调用核心处理逻辑
        result = process_citation_request(request_data)
        
        # 检查是否有错误
        if 'error' in result:
            print(f"Processing error: {result['error']}", file=sys.stderr)
            return {
                'statusCode': 500,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS'
                },
                'body': json.dumps(result)
            }

        print(f"Processing successful, returning result", file=sys.stderr)
        
        # 返回成功响应
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST, OPTIONS'
            },
            'body': json.dumps(result)
        }

    except Exception as e:
        print(f"Handler error: {e}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }