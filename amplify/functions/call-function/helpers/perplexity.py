##### PERPLEXITY USAGE FOR INITIAL METADATA #####
#url = "https://www.nbcnews.com/news/us-news/family-boulder-attack-suspect-taken-ice-custody-dhs-chief-k<PERSON><PERSON>-noem-s-rcna210711" #url from perplexity
#meta1, csl_json1 = extract_article_metadata(url)

import requests
import sys
import os

def call_perplexity_api(prompt: str): #-> tuple[str,str, bool]:
    endpoint = "https://api.perplexity.ai/chat/completions"

    # Get API key from environment variable, fallback to hardcoded for backward compatibility
    api_key = os.environ.get("PERPLEXITY_API_KEY", "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")

    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    # Use the correct payload format according to official documentation
    payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that finds academic sources. You must respond with sources in the exact format: URL: [url] TITLE: [title] Research paper: [yes/no] for each source, separated by double newlines."
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }
    

    try:
        print(f'CITEAI DEBUG: Calling Perplexity API with prompt: {prompt[:200]}...', file=sys.stderr)
        
        resp = requests.post(endpoint, headers=headers, json=payload, timeout=60)
        resp.raise_for_status()
        data = resp.json()
        content = data["choices"][0]["message"]["content"]
        
        print(f'CITEAI DEBUG: Perplexity API response length: {len(content)}', file=sys.stderr)
        print(f'CITEAI DEBUG: Perplexity API response: {content}', file=sys.stderr)

        # Return the raw content string - let parse_multiple_sources handle the parsing
        return content
        
    except Exception as e:
        print(f'CITEAI ERROR: Perplexity API call failed: {e}', file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return ""
