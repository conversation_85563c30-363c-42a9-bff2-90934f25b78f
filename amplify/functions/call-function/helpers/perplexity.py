##### PERPLEXITY USAGE FOR INITIAL METADATA #####
#url = "https://www.nbcnews.com/news/us-news/family-boulder-attack-suspect-taken-ice-custody-dhs-chief-krisi-noem-s-rcna210711" #url from perplexity
#meta1, csl_json1 = extract_article_metadata(url)

import requests
import sys
import json
import re

def call_perplexity_api(prompt: str): #-> tuple[str,str, bool]:
    endpoint = "https://api.perplexity.ai/chat/completions"
    api_key = "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"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    json_schema = {
        "schema": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "url": {"type": "string"},
                    "title": {"type": "string"},
                    "is_paper": {"type": "boolean"}
                },
                "required": ["url", "title", "is_paper"]
            }
        }
    }

    payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that finds academic sources. You must respond with sources in the exact format: URL: [url] TITLE: [title] Research paper: [yes/no] for each source, separated by double newlines."
            },
            {
                "role": "user",
                "content": prompt
            },
        ],
        "response_format": {"type": "text"}
    }
    

    try:
        print(f'CITEAI DEBUG: Calling Perplexity API with prompt: {prompt[:200]}...', file=sys.stderr)
        
        resp = requests.post(endpoint, headers=headers, json=payload, timeout=60)
        resp.raise_for_status()
        data = resp.json()
        content = data["choices"][0]["message"]["content"]
        
        print(f'CITEAI DEBUG: Perplexity API response length: {len(content)}', file=sys.stderr)
        print(f'CITEAI DEBUG: Perplexity API response: {content}', file=sys.stderr)
        
        # Try to parse the content as JSON first
        try:
            parsed_content = json.loads(content)
            if isinstance(parsed_content, list):
                print(f'CITEAI DEBUG: Parsed JSON list with {len(parsed_content)} items', file=sys.stderr)
                return parsed_content
            elif isinstance(parsed_content, dict) and parsed_content.get('url'):
                print(f'CITEAI DEBUG: Parsed JSON dict with url', file=sys.stderr)
                return [parsed_content]
        except json.JSONDecodeError:
            print(f'CITEAI DEBUG: JSON parsing failed, trying text parsing', file=sys.stderr)
        
        # If JSON parsing fails, try to extract structured data from text
        sources = []
        
        # Try multiple splitting strategies
        blocks = []
        if '\n\n' in content:
            blocks = [b.strip() for b in content.split('\n\n') if b.strip()]
        elif '\n' in content:
            blocks = [b.strip() for b in content.split('\n') if b.strip()]
        else:
            blocks = [content.strip()]
            
        print(f'CITEAI DEBUG: Found {len(blocks)} blocks in response', file=sys.stderr)
        
        for i, block in enumerate(blocks):
            print(f'CITEAI DEBUG: Processing block {i}: {block[:100]}...', file=sys.stderr)
            
            # Try multiple regex patterns for each field
            url_match = re.search(r'URL:\s*(.+)', block) or re.search(r'url:\s*(.+)', block) or re.search(r'URL\s*(.+)', block)
            title_match = re.search(r'TITLE:\s*(.+)', block) or re.search(r'title:\s*(.+)', block) or re.search(r'TITLE\s*(.+)', block)
            is_paper_match = re.search(r'Research paper:\s*(yes|no)', block, re.IGNORECASE) or re.search(r'research paper:\s*(yes|no)', block, re.IGNORECASE) or re.search(r'is_paper:\s*(yes|no)', block, re.IGNORECASE)
            
            if url_match and title_match and is_paper_match:
                source = {
                    'url': url_match.group(1).strip(),
                    'title': title_match.group(1).strip(),
                    'is_paper': is_paper_match.group(1).strip().lower() == 'yes'
                }
                sources.append(source)
                print(f'CITEAI DEBUG: Extracted source: {source}', file=sys.stderr)
            else:
                print(f'CITEAI DEBUG: Block {i} did not match expected format', file=sys.stderr)
                print(f'CITEAI DEBUG: URL match: {bool(url_match)}, Title match: {bool(title_match)}, Paper match: {bool(is_paper_match)}', file=sys.stderr)
        
        if sources:
            print(f'CITEAI DEBUG: Successfully extracted {len(sources)} sources from text', file=sys.stderr)
            return sources
        
        print(f'CITEAI DEBUG: No structured data found, returning empty list', file=sys.stderr)
        return []
        
    except Exception as e:
        print(f'CITEAI ERROR: Perplexity API call failed: {e}', file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return []
