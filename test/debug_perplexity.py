#!/usr/bin/env python3

import requests
import json
import sys

def test_perplexity_api():
    """Test the Perplexity API directly"""
    
    endpoint = "https://api.perplexity.ai/chat/completions"
    api_key = "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"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test prompt
    prompt = """a paper is being written about "Commemoration of <PERSON> as Commanding General". find up to 1 original sources (no duplicates) which would have been used to generate the excerpt below. provide me with a url & title of the source and specify if it is a research paper or not. the fields you provide should be for a source that relatively matches the topic provided. give it in this format and do not provide any extra information besides this
URL: [url] 
 TITLE: [title]
 Research paper: [yes/no]

excerpt: As collective memory is the conceptual frame for evaluating the influence on <PERSON>'s commemoration, it is crucial to explain what exactly the term implicates. Collective memory's manner of manifestation is certainly tangible but it is ultimately meant to be regarded abstractly. In theory, collective memory is a social adaptation in interpreting history and its associations. In effect, collective memory conceptualizes itself from individual's ideas into a phenomenon; a tool at the hands of society. The idea of this transfer from concrete to conceptual is explained by Gedi & Elam (1996) where it is stated, "Society thus functions as a location...where concrete individuals are capable of transforming their obscure image into clear concepts" (p. 9). As an influential factor in the analysis of the Confederate Army, Southerners lay importance on how Lee is viewed for generations ahead, so they developed individual ideas for the image"""
    
    # Test 1: Without JSON schema (simple)
    print("=== TEST 1: Simple format (no JSON schema) ===")
    simple_payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant that finds academic sources."
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }
    
    try:
        resp = requests.post(endpoint, headers=headers, json=simple_payload, timeout=60)
        print(f"Status Code: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            content = data["choices"][0]["message"]["content"]
            print(f"Response: {content}")
        else:
            print(f"Error: {resp.text}")
    except Exception as e:
        print(f"Exception: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test 2: With JSON schema (your current format)
    print("=== TEST 2: With JSON schema ===")
    json_schema = {
        "schema": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "url": {"type": "string"},
                    "title": {"type": "string"},
                    "is_paper": {"type": "boolean"}
                },
                "required": ["url", "title", "is_paper"]
            }
        }
    }

    schema_payload = {
        "model": "sonar",
        "messages": [
            {
                "role": "system",
                "content": "You are a concise and precise assistant. Only respond with a JSON array of objects exactly matching the requested schema. Do not include any additional text, summary, or notes."
            },
            {
                "role": "user",
                "content": prompt
            },
        ],
        "response_format": {
            "type": "json_schema",
            "json_schema": json_schema
        }
    }
    
    try:
        resp = requests.post(endpoint, headers=headers, json=schema_payload, timeout=60)
        print(f"Status Code: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            content = data["choices"][0]["message"]["content"]
            print(f"Response: {content}")
            try:
                parsed = json.loads(content)
                print(f"Parsed JSON: {parsed}")
            except Exception as e:
                print(f"JSON Parse Error: {e}")
        else:
            print(f"Error: {resp.text}")
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_perplexity_api()